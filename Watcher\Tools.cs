using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using Windows.System;
using System.Diagnostics;
using CommunityToolkit.Mvvm.DependencyInjection;
using System.Threading.Tasks;

namespace Watcher
{
    public static class Tools
    {
        public static string GetTimeString(DateTime time)
        {
            return time.ToString("yyyy-MM-dd HH:mm:ss");
        }
        // 从字符串中提取日期
        // 594329021_1_20241020T094457Z_20241020T101458Z.mp4
        // 检查格式是否符合
        private static bool IsTimeFormatValid(string input)
        {
            string pattern = @"^\d+_\d+_\d{8}T\d{6}Z_\d{8}T\d{6}Z\.mp4$";
            return Regex.IsMatch(input, pattern);
        }

        /// <summary>
        /// 从文件名中提取日期时间信息
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>开始时间和结束时间的元组</returns>
        public static (DateTime? startTime, DateTime? endTime) ExtractDates(string fileName)
        {
            // 处理转换后的新格式：文件名末尾带Z.mp4或_Z.mp4的文件
            // 例如：20250626-164507_20250626-173002Z.mp4 或 20250626-210001_20250626-212951_Z.mp4
            if (fileName.EndsWith("Z.mp4") || fileName.EndsWith("_Z.mp4"))
            {
                // 移除末尾的Z.mp4或_Z.mp4，然后添加.dav来处理
                fileName = fileName.Substring(0, fileName.Length - 5) + ".dav";
            }
            
            // 匹配原始格式：594329021_1_20241020T094457Z_20241020T101458Z.mp4
            var pattern1 = @"(\d{4})(\d{2})(\d{2})T(\d{2})(\d{2})(\d{2})Z_(\d{4})(\d{2})(\d{2})T(\d{2})(\d{2})(\d{2})Z";
            var match1 = Regex.Match(fileName, pattern1);
            
            if (match1.Success)
            {
                try
                {
                    // 提取开始时间
                    var startYear = int.Parse(match1.Groups[1].Value);
                    var startMonth = int.Parse(match1.Groups[2].Value);
                    var startDay = int.Parse(match1.Groups[3].Value);
                    var startHour = int.Parse(match1.Groups[4].Value);
                    var startMinute = int.Parse(match1.Groups[5].Value);
                    var startSecond = int.Parse(match1.Groups[6].Value);

                    // 提取结束时间
                    var endYear = int.Parse(match1.Groups[7].Value);
                    var endMonth = int.Parse(match1.Groups[8].Value);
                    var endDay = int.Parse(match1.Groups[9].Value);
                    var endHour = int.Parse(match1.Groups[10].Value);
                    var endMinute = int.Parse(match1.Groups[11].Value);
                    var endSecond = int.Parse(match1.Groups[12].Value);

                    var startTime = new DateTime(startYear, startMonth, startDay, startHour, startMinute, startSecond);
                    var endTime = new DateTime(endYear, endMonth, endDay, endHour, endMinute, endSecond);

                    return (startTime, endTime);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解析日期时间时发生错误: {ex.Message}");
                    return (null, null);
                }
            }

            // 匹配新格式：(5)1.2.3.5.6号岗、物流日用品_1_20250705_180002.dav
            // 匹配类似 20250705_180002 的时间格式
            // 同时支持带连字符的格式：20250626-164507_20250626-173002_.mp4 或 20250626-164507_20250626-173002Z.mp4
            var pattern2 = @"(\d{4})(\d{2})(\d{2})[-_](\d{2})(\d{2})(\d{2})";
            var matches = Regex.Matches(fileName, pattern2);
            
            if (matches.Count >= 2)
            {
                try
                {
                    // 第一个匹配作为开始时间，第二个作为结束时间
                    var startMatch = matches[0];
                    var endMatch = matches[1];

                    // 提取开始时间
                    var startYear = int.Parse(startMatch.Groups[1].Value);
                    var startMonth = int.Parse(startMatch.Groups[2].Value);
                    var startDay = int.Parse(startMatch.Groups[3].Value);
                    var startHour = int.Parse(startMatch.Groups[4].Value);
                    var startMinute = int.Parse(startMatch.Groups[5].Value);
                    var startSecond = int.Parse(startMatch.Groups[6].Value);

                    // 提取结束时间
                    var endYear = int.Parse(endMatch.Groups[1].Value);
                    var endMonth = int.Parse(endMatch.Groups[2].Value);
                    var endDay = int.Parse(endMatch.Groups[3].Value);
                    var endHour = int.Parse(endMatch.Groups[4].Value);
                    var endMinute = int.Parse(endMatch.Groups[5].Value);
                    var endSecond = int.Parse(endMatch.Groups[6].Value);

                    var startTime = new DateTime(startYear, startMonth, startDay, startHour, startMinute, startSecond);
                    var endTime = new DateTime(endYear, endMonth, endDay, endHour, endMinute, endSecond);

                    return (startTime, endTime);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解析新格式日期时间时发生错误: {ex.Message}");
                    return (null, null);
                }
            }
            
            if (matches.Count == 1)
            {
                try
                {
                    // 如果只有一个时间，用它作为开始时间，结束时间加1小时
                    var match = matches[0];
                    var year = int.Parse(match.Groups[1].Value);
                    var month = int.Parse(match.Groups[2].Value);
                    var day = int.Parse(match.Groups[3].Value);
                    var hour = int.Parse(match.Groups[4].Value);
                    var minute = int.Parse(match.Groups[5].Value);
                    var second = int.Parse(match.Groups[6].Value);

                    var startTime = new DateTime(year, month, day, hour, minute, second);
                    var endTime = startTime.AddHours(1); // 默认加1小时
                    
                    return (startTime, endTime);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解析单个时间格式时发生错误: {ex.Message}");
                    return (null, null);
                }
            }

            return (null, null);
        }

        /// <summary>
        /// 获取指定时间所在的时间范围（例如，整点到整点）
        /// </summary>
        /// <param name="currentTime">当前时间</param>
        /// <returns>时间范围的开始和结束时间</returns>
        public static (DateTime start, DateTime end) GetTimeRange(DateTime currentTime)
        {
            // 获取当前时间所在的整点小时
            var start = new DateTime(currentTime.Year, currentTime.Month, currentTime.Day, currentTime.Hour, 0, 0);
            var end = start.AddHours(1);

            return (start, end);
        }

        /// <summary>
        /// 根据文件名中的日期进行过滤
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="targetDate">目标日期</param>
        /// <returns>文件名中的日期是否与目标日期匹配</returns>
        public static bool FilterByDateInFileName(string fileName, DateTime targetDate)
        {
            try
            {
                // 使用已有的ExtractDates函数提取日期
                var (startTime, endTime) = ExtractDates(fileName);
                
                if (startTime.HasValue)
                {
                    // 比较日期部分（忽略时间）
                    return startTime.Value.Date == targetDate.Date;
                }
                
                // 如果ExtractDates无法解析，尝试使用FileListViewModel中的简单日期解析
                var fileNameWithoutExt = System.IO.Path.GetFileNameWithoutExtension(fileName);
                var dateStr = fileNameWithoutExt.Split('_')[0];
                if (DateTime.TryParseExact(dateStr, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var fileDate))
                {
                    return fileDate.Date == targetDate.Date;
                }
            }
            catch
            {
                // 解析失败时返回false
            }
            
            return false;
        }

        /// <summary>
        /// 根据文件名中的日期范围进行过滤
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>文件名中的日期是否在给定范围内</returns>
        public static bool FilterByDateRangeInFileName(string fileName, DateTime startDate, DateTime endDate)
        {
            try
            {
                var (startTime, endTime) = ExtractDates(fileName);
                
                if (startTime.HasValue)
                {
                    return startTime.Value.Date >= startDate.Date && startTime.Value.Date <= endDate.Date;
                }
                
                // 备用解析
                var fileNameWithoutExt = System.IO.Path.GetFileNameWithoutExtension(fileName);
                var dateStr = fileNameWithoutExt.Split('_')[0];
                if (DateTime.TryParseExact(dateStr, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var fileDate))
                {
                    return fileDate.Date >= startDate.Date && fileDate.Date <= endDate.Date;
                }
            }
            catch
            {
                // 解析失败时返回false
            }
            
            return false;
        }

        static DateTime GetTimeFromProgress(DateTime startTime, DateTime endTime, double progressPercentage)
        {
            // 检查进度百分比是否在0到100之间
            if (progressPercentage < 0 || progressPercentage > 100)
            {
                throw new ArgumentOutOfRangeException(nameof(progressPercentage), "进度百分比必须在0到100之间。");
            }

            // 检查起始时间是否早于结束时间
            if (startTime >= endTime)
            {
                throw new ArgumentException("起始时间必须早于结束时间。");
            }

            // 计算总时间差
            TimeSpan totalDuration = endTime - startTime;

            // 计算已过的时间
            TimeSpan elapsedTime = TimeSpan.FromTicks((long)(totalDuration.Ticks * (progressPercentage / 100.0)));

            // 计算对应的时间
            DateTime correspondingTime = startTime + elapsedTime;

            return correspondingTime;
        }
    }

    /// <summary>
    /// 全局键盘输入处理服务
    /// </summary>
    public static class GlobalKeyboardInputService
    {
        /// <summary>
        /// 处理数字键输入
        /// </summary>
        /// <param name="key">按键</param>
        /// <param name="context">上下文信息（用于判断是否应该处理）</param>
        /// <returns>是否处理了该按键</returns>
        public static bool HandleNumberKeyInput(VirtualKey key, string context = "")
        {
            // 检查是否为数字键
            if (key >= VirtualKey.NumberPad0 && key <= VirtualKey.NumberPad9)
            {
                var overviewViewModel = Ioc.Default.GetRequiredService<OverviewViewModel>();
                char digit = (char)('0' + ((int)key - (int)VirtualKey.NumberPad0));
                overviewViewModel.AddDigitToInput(digit);
                Debug.WriteLine($"[{context}] 输入数字: {digit}, 当前价格: {overviewViewModel.CurrentInputPrice}");
                return true;
            }
            return false;
        }

        /// <summary>
        /// 处理回车键输入
        /// </summary>
        /// <param name="key">按键</param>
        /// <param name="realTime">真实时间</param>
        /// <param name="context">上下文信息</param>
        /// <returns>是否处理了该按键</returns>
        public static async Task<bool> HandleEnterKeyInputAsync(VirtualKey key, DateTime realTime, string context = "")
        {
            if (key == VirtualKey.Enter)
            {
                var overviewViewModel = Ioc.Default.GetRequiredService<OverviewViewModel>();
                
                if (string.IsNullOrEmpty(overviewViewModel.CurrentInputPrice))
                    return false;
                
                // 确认输入，添加收费记录
                await overviewViewModel.ConfirmInput(realTime);
                Debug.WriteLine($"[{context}] 添加收费记录: {realTime}, 价格: {overviewViewModel.CurrentInputPrice}");
                return true;
            }
            return false;
        }

        /// <summary>
        /// 统一处理键盘输入（数字键和回车键）
        /// </summary>
        /// <param name="key">按键</param>
        /// <param name="realTime">真实时间</param>
        /// <param name="context">上下文信息</param>
        /// <returns>是否处理了该按键</returns>
        public static async Task<bool> HandleKeyboardInputAsync(VirtualKey key, DateTime realTime, string context = "")
        {
            // 先尝试处理数字键
            if (HandleNumberKeyInput(key, context))
                return true;
            
            // 再尝试处理回车键
            if (await HandleEnterKeyInputAsync(key, realTime, context))
                return true;
            
            return false;
        }
    }
}
