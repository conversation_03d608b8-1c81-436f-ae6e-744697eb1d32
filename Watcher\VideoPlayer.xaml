<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="Watcher.VideoPlayer"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:Watcher"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:winui="using:FlyleafLib.Controls.WinUI"
    d:DataContext="{d:DesignInstance Type=local:PlayerViewModel}"
    mc:Ignorable="d"
>

    <Grid DataContext="{x:Bind ViewModel}">
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>
        
        <winui:FlyleafHost x:Name="flyleafHost" Player="{Binding Player}" />
        <Grid Grid.Row="1" Margin="16 0 16 8" Padding="12" CornerRadius="0 0 8 8" Background="#88FFFFFF">
            
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <StackPanel Orientation="Horizontal">
                    <Button Content="播放" Click="Play_Click" />
                    <Button Content="暂停" Click="Pause_Click" />
                    <Button Content="停止" Click="Stop_Click" />
                    <TextBlock Text="{Binding DurationText}" Margin="12"/>

                </StackPanel>
                <Slider x:Name="progressSlider" 
                        Grid.Column="1" 
                        Maximum="{Binding ProgressMaximumLong}" 
                        Value="{Binding CurTimeLong, Mode=OneWay}"
                        IsEnabled="{Binding ProgressBarEnabled}"
                        VerticalAlignment="Center"
                        IsTabStop="False"
                        PointerPressed="ProgressSlider_PointerPressed"
                        PointerCaptureLost="ProgressSlider_PointerCaptureLost"
                        ValueChanged="ProgressSlider_ValueChanged"
                        PointerMoved="ProgressSlider_PointerMoved">
                    <ToolTipService.ToolTip>
                        <ToolTip Content="{Binding ProgressToolTipText, Mode=OneWay}" />
                    </ToolTipService.ToolTip>
                </Slider>
            </Grid>
        </Grid>

    </Grid>
</Page>
