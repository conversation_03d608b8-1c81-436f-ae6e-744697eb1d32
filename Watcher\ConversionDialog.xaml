<?xml version="1.0" encoding="utf-8"?>
<ContentDialog
    x:Class="Watcher.ConversionDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:Watcher"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="视频转换"
    PrimaryButtonText=""
    SecondaryButtonText=""
    CloseButtonText=""
    DefaultButton="Primary"
    MinWidth="500"
    MinHeight="400">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 状态信息 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="{x:Bind ViewModel.StatusMessage, Mode=OneWay}" 
                       FontSize="14" 
                       Margin="0,0,0,10"/>
            <TextBlock Text="{x:Bind ViewModel.CurrentFileMessage, Mode=OneWay}" 
                       FontSize="12" 
                       Foreground="Gray"/>
        </StackPanel>

        <!-- 控制按钮 -->
        <StackPanel Grid.Row="1" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center"
                    Margin="0,0,0,20">
            <Button x:Name="StartButton"
                    Content="开始转换"
                    Command="{x:Bind ViewModel.StartConversionCommand}"
                    IsEnabled="{x:Bind ViewModel.CanStart, Mode=OneWay}"
                    Margin="0,0,10,0"
                    MinWidth="100"/>
            <Button x:Name="StopButton"
                    Content="停止转换"
                    Command="{x:Bind ViewModel.StopConversionCommand}"
                    IsEnabled="{x:Bind ViewModel.CanStop, Mode=OneWay}"
                    Margin="0,0,10,0"
                    MinWidth="100"/>
            <Button x:Name="CloseButton"
                    Content="关闭"
                    Command="{x:Bind ViewModel.CloseDialogCommand}"
                    IsEnabled="{x:Bind ViewModel.CanClose, Mode=OneWay}"
                    MinWidth="100"/>
        </StackPanel>

        <!-- 进度条 -->
        <StackPanel Grid.Row="2" Margin="0,0,0,20">
            <TextBlock Text="{x:Bind ViewModel.ProgressText, Mode=OneWay}" 
                       Margin="0,0,0,5"
                       FontSize="12"/>
            <ProgressBar Value="{x:Bind ViewModel.Progress, Mode=OneWay}"
                         Maximum="{x:Bind ViewModel.MaxProgress, Mode=OneWay}"
                         Height="20"
                         IsIndeterminate="{x:Bind ViewModel.IsIndeterminate, Mode=OneWay}"/>
        </StackPanel>

        <!-- 控制台消息展开器 -->
        <Expander Grid.Row="3"
                  Header="FFmpeg 控制台输出"
                  IsExpanded="False"
                  HorizontalAlignment="Stretch"
                  Margin="0,0,0,10">
            <ScrollViewer MaxHeight="200" 
                          VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Auto">
                <TextBlock x:Name="ConsoleOutput"
                           Text="{x:Bind ViewModel.ConsoleOutput, Mode=OneWay}"
                           FontFamily="Consolas"
                           FontSize="10"
                           Foreground="Gray"
                           TextWrapping="Wrap"
                           Margin="10"/>
            </ScrollViewer>
        </Expander>

        <!-- 占位空间 -->
        <Grid Grid.Row="4"/>
    </Grid>
</ContentDialog>
