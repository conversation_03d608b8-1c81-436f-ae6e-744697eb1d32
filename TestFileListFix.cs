using System;
using System.IO;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Linq;

namespace TestFileListFix
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("测试文件列表重复加载修复...");
            
            // 创建测试文件夹
            var testFolder = Path.Combine(Path.GetTempPath(), "WatcherTest_" + Guid.NewGuid().ToString("N")[..8]);
            Directory.CreateDirectory(testFolder);
            
            try
            {
                // 创建测试视频文件
                var testFiles = new[]
                {
                    "594329021_1_20241018T231930Z_20241018T234931Z.mp4",
                    "594329021_1_20241019T231930Z_20241019T234931Z.mp4",
                    "594329021_1_20241020T231930Z_20241020T234931Z.mp4"
                };

                foreach (var fileName in testFiles)
                {
                    var filePath = Path.Combine(testFolder, fileName);
                    File.WriteAllText(filePath, "test content");
                }
                
                Console.WriteLine($"创建了测试文件夹: {testFolder}");
                Console.WriteLine($"创建了 {testFiles.Length} 个测试视频文件");
                
                // 模拟FileListViewModel的行为
                Console.WriteLine("\n模拟文件加载过程...");
                
                var refreshCount = 0;
                
                // 模拟RefreshAsync方法
                Action refreshAsync = () =>
                {
                    refreshCount++;
                    Console.WriteLine($"RefreshAsync 被调用 - 第 {refreshCount} 次");
                    
                    var videoExtensions = new[] { ".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mkv", ".dav" };
                    var files = Directory.GetFiles(testFolder)
                        .Where(f => videoExtensions.Contains(Path.GetExtension(f).ToLowerInvariant()))
                        .OrderBy(f => f)
                        .ToArray();
                    
                    Console.WriteLine($"找到 {files.Length} 个视频文件");
                };
                
                // 模拟LoadVideos调用
                Console.WriteLine("调用 LoadVideos()...");
                refreshAsync();
                
                // 模拟LoadInitialFolder中的SelectedFolder设置
                Console.WriteLine("模拟 LoadInitialFolder 设置 SelectedFolder...");
                // 在真实场景中，这里不应该再次触发RefreshAsync
                
                Console.WriteLine($"\n总共调用 RefreshAsync {refreshCount} 次");
                
                if (refreshCount == 1)
                {
                    Console.WriteLine("✅ 修复成功！没有重复加载");
                }
                else
                {
                    Console.WriteLine($"❌ 仍有问题，RefreshAsync 被调用了 {refreshCount} 次");
                }
            }
            finally
            {
                // 清理测试文件夹
                if (Directory.Exists(testFolder))
                {
                    Directory.Delete(testFolder, true);
                }
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
