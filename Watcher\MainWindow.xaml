<?xml version="1.0" encoding="utf-8"?>
<Window
    x:Class="Watcher.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:Watcher"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    
    mc:Ignorable="d"
    
    Title="Watcher">
    <Window.SystemBackdrop>
        <MicaBackdrop />
    </Window.SystemBackdrop>
    <Grid>
        
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        <local:Overview x:Name="MyOverview" Grid.Column="0"></local:Overview>
        <Grid x:Name="PlayerMask" Grid.Column="1"></Grid>
        <local:VideoPlayer Grid.Column="1" Height="{x:Bind PlayerMask.Height}" Width="{x:Bind PlayerMask.Width}" HorizontalAlignment="Stretch" x:Name="MyPlayer"></local:VideoPlayer>
        
        <local:FileListView Grid.Column="2" x:Name="MyListView"></local:FileListView>
    </Grid>
</Window>
