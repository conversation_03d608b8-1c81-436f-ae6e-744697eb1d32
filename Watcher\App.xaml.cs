using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Runtime.InteropServices.WindowsRuntime;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Controls.Primitives;
using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;
using Microsoft.UI.Xaml.Navigation;
using Microsoft.UI.Xaml.Shapes;
using Windows.ApplicationModel;
using Windows.ApplicationModel.Activation;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Microsoft.Extensions.DependencyInjection;
using CommunityToolkit.Mvvm.DependencyInjection;
using Watcher.Services;

// To learn more about WinUI, the WinUI project structure,
// and more about our project templates, see: http://aka.ms/winui-project-info.

namespace Watcher
{
    /// <summary>
    /// Provides application-specific behavior to supplement the default Application class.
    /// </summary>
    public partial class App : Application
    {
        /// <summary>
        /// Initializes the singleton application object.  This is the first line of authored code
        /// executed, and as such is the logical equivalent of main() or WinMain().
        /// </summary>
        public App()
        {
            InitializeDatabase();
            ConfigureServices();
            this.InitializeComponent();
        }

        private void InitializeDatabase()
        {
            try
            {
                DatabaseInitializationService.InitializeAllDatabases();
            }
            catch (Exception ex)
            {
                // 记录错误但不阻止应用启动
                System.Diagnostics.Debug.WriteLine($"数据库初始化失败: {ex.Message}");
            }
        }

        private void ConfigureServices()
        {
            Ioc.Default.ConfigureServices(
                new ServiceCollection()
                .AddSingleton<ConfigurationService>()
                .AddSingleton<VehicleCountService>()
                .AddSingleton<PlayerViewModel>()
                .AddSingleton<OverviewViewModel>()
                .AddSingleton<FileListViewModel>()
                .BuildServiceProvider());
        }

        /// <summary>
        /// Invoked when the application is launched.
        /// </summary>
        /// <param name="args">Details about the launch request and process.</param>
        protected override void OnLaunched(Microsoft.UI.Xaml.LaunchActivatedEventArgs args)
        {
            m_window = new MainWindow();
            m_window.Activate();
        }

        private Window? m_window;

        public MainWindow? GetMainWindow() => m_window as MainWindow;
    }

    /// <summary>
    /// 窗口句柄扩展方法
    /// </summary>
    public static class WindowExtensions
    {
        [ComImport]
        [Guid("EECDBF0E-BAE9-4CB6-A68E-9598E1CB57BB")]
        [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
        private interface IWindowNative
        {
            IntPtr WindowHandle { get; }
        }

        public static IntPtr GetWindowHandle(this Window window)
        {
            // Use the more robust approach to get the window handle
            if (window == null)
                throw new ArgumentNullException(nameof(window));

            // Try to get the HWND using the Window Native API
            try
            {
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(window);
                return hwnd;
            }
            catch (InvalidCastException)
            {
                // Fallback method if the above fails
                var windowNative = (IWindowNative)window;
                return windowNative.WindowHandle;
            }
        }
    }
}
