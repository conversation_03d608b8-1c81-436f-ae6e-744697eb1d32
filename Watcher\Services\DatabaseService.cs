using SqlSugar;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Watcher.Models;

namespace Watcher.Services
{
    /// <summary>
    /// 数据库服务类
    /// </summary>
    public class DatabaseService
    {
        private readonly SqlSugarClient _db;

        public DatabaseService()
        {
            // 使用数据库初始化服务提供的路径
            var connectionString = $"Data Source={DatabaseInitializationService.GetDbPath()}";
            
            _db = new SqlSugarClient(new ConnectionConfig
            {
                ConnectionString = connectionString,
                DbType = DbType.Sqlite,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute
            });

            // 初始化数据库表
            InitializeDatabase();
        }

        /// <summary>
        /// 初始化数据库表结构
        /// </summary>
        private void InitializeDatabase()
        {
            _db.CodeFirst.InitTables(typeof(ChargeRecord));
            _db.CodeFirst.InitTables<VehicleEntry>();
        }

        /// <summary>
        /// 添加收费记录
        /// </summary>
        /// <returns>新插入记录的ID</returns>
        public async Task<int> AddChargeRecordAsync(ChargeRecord record)
        {
            return await _db.Insertable(record).ExecuteReturnIdentityAsync();
        }

        /// <summary>
        /// 获取所有收费记录
        /// </summary>
        public async Task<List<ChargeRecord>> GetAllChargeRecordsAsync()
        {
            return await _db.Queryable<ChargeRecord>()
                .OrderByDescending(r => r.ChargeTime)
                .ToListAsync();
        }

        /// <summary>
        /// 删除收费记录
        /// </summary>
        public async Task DeleteChargeRecordAsync(int id)
        {
            await _db.Deleteable<ChargeRecord>(r => r.Id == id).ExecuteCommandAsync();
        }

        /// <summary>
        /// 获取今日收费记录
        /// </summary>
        public async Task<List<ChargeRecord>> GetTodayRecordsAsync()
        {
            var today = DateTime.Today;
            return await _db.Queryable<ChargeRecord>()
                .Where(r => r.ChargeTime >= today)
                .OrderByDescending(r => r.ChargeTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取今日总收入
        /// </summary>
        public async Task<decimal> GetTodayTotalAsync()
        {
            var today = DateTime.Today;
            return await _db.Queryable<ChargeRecord>()
                .Where(r => r.ChargeTime >= today)
                .SumAsync(r => r.Amount);
        }
    }
}