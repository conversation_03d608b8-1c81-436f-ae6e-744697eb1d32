<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="Watcher.Overview"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:Watcher"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    xmlns:controls="using:CommunityToolkit.WinUI.Controls"
>
    <Page.Resources>
        <local:TimeToHourConverter x:Key="TimeToHourConverter" />
    </Page.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>
        <TextBlock x:Name="ChargeTitleBlock"
                Margin="8"
                HorizontalAlignment="Center"
                FontSize="16"
                FontWeight="Bold"
                Text="{Binding ChargeTitle}"></TextBlock>

        <ListView x:Name="ChargeItemsListBox" Grid.Row="1" ItemsSource="{Binding FilteredChargeItems}"
                  ScrollViewer.VerticalScrollBarVisibility="Auto"
                  ScrollViewer.VerticalScrollMode="Enabled"
                  ScrollViewer.HorizontalScrollMode="Disabled"
                  ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                  ScrollViewer.IsVerticalRailEnabled="True"
                  ScrollViewer.IsHorizontalRailEnabled="False"
                  VirtualizingStackPanel.VirtualizationMode="Recycling"
                  SelectionMode="Extended"
                  >
            <ListView.Header>
                <Grid Padding="16,12"
                      ColumnSpacing="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="24" />
                        <ColumnDefinition Width="96" />
                        <ColumnDefinition Width="96" />
                        <ColumnDefinition Width="96" />
                    </Grid.ColumnDefinitions>
                    <Grid/>
                    <TextBlock Grid.Column="1" Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                               Text="时间" 
                               FontWeight="Bold" />
                    <TextBlock Grid.Column="2"
                               Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                               Text="项目名称" 
                               FontWeight="Bold" />
                    <TextBlock Grid.Column="3"
                               Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                               Text="价格" 
                               FontWeight="Bold" />
                </Grid>
            </ListView.Header>
            <ListView.ItemTemplate>
                <DataTemplate x:DataType="local:ChargeItem">
                    <Grid ColumnSpacing="16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="24" />
                            <ColumnDefinition Width="96" />
                            <ColumnDefinition Width="96" />
                            <ColumnDefinition Width="96" />
                        </Grid.ColumnDefinitions>
                        <Grid></Grid>
                        <TextBlock Grid.Column="1" Text="{x:Bind FormattedTime}" />
                        <TextBlock Grid.Column="2"
                                   Text="{x:Bind Name}" />
                        <TextBlock Grid.Column="3"
                                   Text="{x:Bind FormattedPrice}" />
                    </Grid>
                </DataTemplate>
            </ListView.ItemTemplate>
            <ListView.ItemContainerStyle>
                <Style BasedOn="{StaticResource DefaultListViewItemStyle}"
                       TargetType="ListViewItem">
                    <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                </Style>
            </ListView.ItemContainerStyle>
        </ListView>

        <!-- 当前输入显示区域 -->
        <Border Grid.Row="2" 
                Background="{Binding InputBackgroundColor}" 
                CornerRadius="5" 
                Margin="8,0,8,8"
                Padding="10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="{Binding CurrentInputDisplay}" 
                          FontSize="16" 
                          FontWeight="Bold" 
                          Foreground="{Binding InputForegroundColor}"
                           VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <StackPanel Grid.Row="3" Margin="6" Orientation="Horizontal">
            <Grid x:Name="ButtonsLayout">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Button Grid.Column="0" x:Name="DeleteButton"
                        IsEnabled="False"
                        ToolTipService.ToolTip="删除选中的收费记录">
                    删除
                </Button>
                <Button Grid.Column="2" x:Name="ExportButton" HorizontalContentAlignment="Right" Click="ExportButton_Click">导出</Button>
            </Grid>
        </StackPanel>
    </Grid>
</Page>
