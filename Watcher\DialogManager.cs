using Microsoft.UI.Xaml.Controls;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Watcher
{
    /// <summary>
    /// 对话框管理器，确保同一时间只有一个ContentDialog处于打开状态
    /// </summary>
    public static class DialogManager
    {
        private static readonly Queue<ContentDialog> _dialogQueue = new();
        private static bool _isDialogShowing = false;
        private static readonly object _lockObject = new();

        /// <summary>
        /// 显示对话框，如果已有对话框正在显示，则排队等待
        /// </summary>
        /// <param name="dialog">要显示的对话框</param>
        /// <returns>对话框结果</returns>
        public static async Task<ContentDialogResult> ShowDialogAsync(ContentDialog dialog)
        {
            if (dialog == null)
                throw new ArgumentNullException(nameof(dialog));

            lock (_lockObject)
            {
                if (_isDialogShowing)
                {
                    _dialogQueue.Enqueue(dialog);
                    return ContentDialogResult.None;
                }
                _isDialogShowing = true;
            }

            try
            {
                var result = await dialog.ShowAsync();
                return result;
            }
            catch (Exception)
            {
                // 如果显示失败，尝试下一个对话框
                return ContentDialogResult.None;
            }
            finally
            {
                await ProcessQueueAsync();
            }
        }

        /// <summary>
        /// 处理对话框队列中的下一个对话框
        /// </summary>
        private static async Task ProcessQueueAsync()
        {
            lock (_lockObject)
            {
                if (_dialogQueue.Count == 0)
                {
                    _isDialogShowing = false;
                    return;
                }
            }

            // 延迟一小段时间，确保前一个对话框完全关闭
            await Task.Delay(100);

            ContentDialog nextDialog;
            lock (_lockObject)
            {
                if (_dialogQueue.Count == 0)
                {
                    _isDialogShowing = false;
                    return;
                }
                nextDialog = _dialogQueue.Dequeue();
            }

            try
            {
                await nextDialog.ShowAsync();
            }
            catch (Exception)
            {
                // 忽略显示失败的对话框，继续处理队列
            }
            finally
            {
                await ProcessQueueAsync();
            }
        }

        /// <summary>
        /// 清空对话框队列
        /// </summary>
        public static void ClearQueue()
        {
            lock (_lockObject)
            {
                _dialogQueue.Clear();
                _isDialogShowing = false;
            }
        }

        /// <summary>
        /// 检查当前是否有对话框正在显示
        /// </summary>
        public static bool IsDialogShowing
        {
            get
            {
                lock (_lockObject)
                {
                    return _isDialogShowing || _dialogQueue.Count > 0;
                }
            }
        }
    }
}