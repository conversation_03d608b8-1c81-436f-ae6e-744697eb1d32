using CommunityToolkit.Mvvm.DependencyInjection;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;
using Windows.System;
using System.Diagnostics;

namespace Watcher
{
    public sealed partial class FileListView : UserControl
    {
        FileListViewModel ViewModel { get; set; }

        public FileListView()
        {
            this.InitializeComponent();
            this.ViewModel = Ioc.Default.GetRequiredService<FileListViewModel>();

            // 添加键盘事件处理
            this.Loaded += FileListView_Loaded;
        }

        private void FileListView_Loaded(object sender, RoutedEventArgs e)
        {
            // 找到ListBox并添加键盘事件处理
            var listBox = this.FindChild<ListBox>();
            if (listBox != null)
            {
                listBox.KeyDown += ListBox_KeyDown;
                listBox.IsTabStop = true;
            }
            
            // 订阅ViewModel的文件夹选择事件
            ViewModel.PropertyChanged += ViewModel_PropertyChanged;
        }

        private void ViewModel_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // 移除自动转换逻辑，现在通过转换对话框手动控制
        }

        private void ListBox_KeyDown(object sender, KeyRoutedEventArgs e)
        {
            // 处理空格键，让父窗口处理播放/暂停
            if (e.Key == VirtualKey.Space)
            {
                // 不处理空格键，让事件冒泡到主窗口
                e.Handled = false;
                Debug.WriteLine("ListBox收到空格键，不处理，让主窗口处理");
                return;
            }
        }



        // 辅助方法：查找子控件
        private T? FindChild<T>() where T : DependencyObject
        {
            return FindChild<T>(this);
        }

        private T? FindChild<T>(DependencyObject parent) where T : DependencyObject
        {
            if (parent == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childResult = FindChild<T>(child);
                if (childResult != null)
                    return childResult;
            }
            return null;
        }
    }
}
