using CommunityToolkit.Mvvm.ComponentModel;
using System;
using Watcher.Models;

namespace Watcher
{
    /// <summary>
    /// 收费项目类，用于显示收费记录
    /// </summary>
    public partial class ChargeItem : ObservableObject
    {
        /// <summary>
        /// 数据库记录ID
        /// </summary>
        [ObservableProperty]
        private int id;

        /// <summary>
        /// 收费时间
        /// </summary>
        [ObservableProperty]
        private DateTime time;

        /// <summary>
        /// 项目名称（如：三轮车、摩托车等）
        /// </summary>
        [ObservableProperty]
        private string name = string.Empty;

        /// <summary>
        /// 收费价格（单位：元）
        /// </summary>
        [ObservableProperty]
        private decimal price;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ChargeItem()
        {
            Time = DateTime.Now;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">项目名称</param>
        /// <param name="price">价格</param>
        public ChargeItem(string name, decimal price) : this()
        {
            Name = name;
            Price = price;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="time">时间</param>
        /// <param name="name">项目名称</param>
        /// <param name="price">价格</param>
        public ChargeItem(DateTime time, string name, decimal price) : this()
        {
            Time = time;
            Name = name;
            Price = price;
        }

        /// <summary>
        /// 从数据库记录创建收费项目
        /// </summary>
        /// <param name="record">数据库记录</param>
        public ChargeItem(ChargeRecord record) : this()
        {
            Id = record.Id;
            Time = record.ChargeTime;
            Name = record.VehicleName;
            Price = record.Amount;
        }

        /// <summary>
        /// 格式化时间显示（HH:mm:ss格式）
        /// </summary>
        public string FormattedTime => Time.ToString("HH:mm:ss");

        /// <summary>
        /// 格式化价格显示
        /// </summary>
        public string FormattedPrice => $"{Price}元";

        /// <summary>
        /// 完整的显示字符串，格式：时间 名称 价格
        /// 例如：19:30:20 三轮车 50元
        /// </summary>
        public string DisplayText => $"{FormattedTime} {Name} {FormattedPrice}";

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return DisplayText;
        }
    }
}
