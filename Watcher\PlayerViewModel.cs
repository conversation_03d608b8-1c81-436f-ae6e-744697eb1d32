using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.DependencyInjection;
using FlyleafLib;
using FlyleafLib.MediaPlayer;
using Microsoft.UI.Xaml;
using System;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Text.RegularExpressions;
using Watcher.Services;

namespace Watcher
{
    public partial class PlayerViewModel : ObservableObject
    {
        public Player? Player { get; set; }
        public string FFmpegPath;

        public PlayerViewModel()
        {
            // 从配置服务获取FFmpeg路径
            var configService = Ioc.Default.GetRequiredService<ConfigurationService>();
            FFmpegPath = configService.GetFFmpegPath();
            
            // 初始化进度条为禁用状态
            ProgressBarEnabled = false;
            
            InitPlayer();
            Debug.WriteLine("Player初始化完成");
        }
        private void InitPlayer()
        {
            Engine.Start(new EngineConfig()
            {
#if DEBUG
                LogOutput = ":debug",
                LogLevel = LogLevel.Debug,
                FFmpegLogLevel = Flyleaf.FFmpeg.LogLevel.Warn,
#endif
                FFmpegPath = @":FFmpeg",
                UIRefresh = false, // For Activity Mode usage
                PluginsPath = ":Plugins",
            });
            Player = new Player();
            if (Player == null)
            {
                throw new Exception(
                    "Player初始化不能为null");
            }
            Player.PropertyChanged += Player_PropertyChanged;
        }

        private void Player_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                                    case nameof(Player.Status):
                    Debug.WriteLine($"播放状态变化: {Player?.Status}");
                    if (Player?.IsPlaying != null)
                        IsPlaying = (bool)(Player?.IsPlaying);
                
                // 更新进度条启用状态 - 有视频且不是stopped状态时启用
                ProgressBarEnabled = Player?.Status != Status.Stopped && Player?.Status != Status.Ended;
                Debug.WriteLine($"进度条启用状态: {ProgressBarEnabled}");
                
                // 检查播放是否结束
                    if (Player?.Status != null && Player.Status == Status.Ended)
                    {
                        Debug.WriteLine("视频播放结束��触发自动播放下一个视频");
                        PlaybackEnded?.Invoke(this, EventArgs.Empty);
                    }
                    break;

                case nameof(Player.CurTime):
                    CurTime = TimeSpan.FromTicks(Player?.CurTime ?? 0);
                    CurTimeText = CurTime.ToString(@"hh\:mm\:ss");
                    CurTimeLong = (Player?.CurTime ?? 0);
                    break;
                case nameof(Player.Duration):
                    
                    TimeDurationLong = (Player?.Duration ?? 0);
                    
                    // 计算进度条最大值：总时长减去2秒（2秒 = 20,000,000 ticks）
                    var twoSecondsInTicks = TimeSpan.FromSeconds(2).Ticks;
                    ProgressMaximumLong = Math.Max(0, TimeDurationLong - twoSecondsInTicks);
                    
                    var duration = TimeSpan.FromTicks(Player?.Duration ?? 0);
                    TimeDuration = duration;
                    DurationText = TimeDuration.ToString(@"hh\:mm\:ss");
                    Debug.WriteLine("当前视频长度：" + DurationText);
                    Debug.WriteLine($"进度条最大值设置为：{ProgressMaximumLong} (总长度减去2秒)");
                    break;
            }
        }
       
        [ObservableProperty]
        private string curTimeText;
        [ObservableProperty]
        private TimeSpan curTime;
        [ObservableProperty]
        private double curTimeLong;
        [ObservableProperty] private string durationText;
        [ObservableProperty]
        private double timeDurationLong;

        // 添加进度条最大值属性（总时长减去2秒）
        [ObservableProperty]
        private double progressMaximumLong;

        [ObservableProperty]
        private TimeSpan timeDuration;

        [ObservableProperty]
        private bool isPlaying;

        // 添加进度条启用状态属性
        [ObservableProperty]
        private bool progressBarEnabled;

        [ObservableProperty]
        private string progressToolTipText;
        
        public DateTime? VideoStartTime { get; private set; }


        // 添加播放结束事件
        public event EventHandler PlaybackEnded;

        public TimeSpan CurrentTime =>
                TimeSpan.FromTicks(Player?.CurTime ?? 0);
        public void Play(string path)
        {
            // 从文件名解析开始时间
            VideoStartTime = ParseDateTimeFromFileName(path);
            
            Player?.Open(path);
            Player?.Play();
            Debug.WriteLine($"播放：{path}");
        }
        
        private DateTime? ParseDateTimeFromFileName(string filePath)
        {
            try
            {
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                // 正则表达式匹配文件名中的日期时间部分，例如 CH01_20230718183900
                var match = Regex.Match(fileName, @"_(\d{14})");
                if (match.Success)
                {
                    var dateTimeString = match.Groups[1].Value;
                    if (DateTime.TryParseExact(dateTimeString, "yyyyMMddHHmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime result))
                    {
                        Debug.WriteLine($"从文件名 {fileName} 解析到开始时间: {result}");
                        return result;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从文件名解析日期时间失败: {ex.Message}");
            }

            Debug.WriteLine($"无法从文件名 {Path.GetFileName(filePath)} 解析出有效的开始时间。");
            return null;
        }
        
        /// <summary>
        /// 更新进度条的Tooltip文本
        /// </summary>
        /// <param name="pointerTimeTicks">指针悬停位置对应的时间刻度</param>
        public void UpdateProgressToolTip(long pointerTimeTicks)
        {
            if (VideoStartTime.HasValue)
            {
                var elapsedTime = TimeSpan.FromTicks(pointerTimeTicks);
                var realTime = VideoStartTime.Value.Add(elapsedTime);
            }
            else
            {
                // 如果没有解析到开始时间，则显示常规的播放时间
                var elapsedTime = TimeSpan.FromTicks(pointerTimeTicks);
            }
        }
        
        public void SeekForward() => Player?.SeekForward();
        public void SeekBackward() => Player?.SeekBackward();
        public void Pause()
        {
            Player?.Pause();
        }
        public void Stop()
        {
            Player?.Stop();
        }
        public void Seek(int position)
        {
            Player?.SeekAccurate(position);
        }
        
        public void SeekToTime(long timeTicks)
        {
            if (Player != null)
            {
                Debug.WriteLine($"跳转到时间 (Ticks): {timeTicks}");
                Player.CurTime = timeTicks;
            }
        }

        /// <summary>
        /// 根据秒数跳转到指定时间
        /// </summary>
        /// <param name="seconds">目标时间（秒）</param>
        public void SeekToSeconds(double seconds)
        {
            if (Player != null)
            {
                var timeTicks = TimeSpan.FromSeconds(seconds).Ticks;
                Debug.WriteLine($"跳转到时间 (��): {seconds}, Ticks: {timeTicks}");
                Player.CurTime = timeTicks;
            }
        }

        /// <summary>
        /// 切换播放/暂停状态
        /// </summary>
        public void TogglePlayPause()
        {
            if (Player != null)
            {
                if (IsPlaying)
                {
                    Debug.WriteLine("当前正在播放，切换为暂停");
                    Player.Pause();
                }
                else
                {
                    Debug.WriteLine("当前已暂停，切换为播放");
                    Player.Play();
                }
            }
        }
    }
}
