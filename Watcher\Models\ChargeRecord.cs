using SqlSugar;
using System;

namespace Watcher.Models
{
    /// <summary>
    /// 收费记录数据库模型
    /// </summary>
    [SugarTable("ChargeRecords")]
    public class ChargeRecord
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 收费时间
        /// </summary>
        public DateTime ChargeTime { get; set; }

        /// <summary>
        /// 车辆类型名称
        /// </summary>
        [SugarColumn(Length = 50)]
        public string VehicleName { get; set; } = string.Empty;

        /// <summary>
        /// 收费金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }
}