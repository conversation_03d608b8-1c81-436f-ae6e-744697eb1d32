using System;
using System.IO;
using System.Text.Json;
using Watcher.Models;

namespace Watcher.Services
{
    /// <summary>
    /// 配置管理服务
    /// </summary>
    public class ConfigurationService
    {
        private const string ConfigDirectory = "D:\\Watcher";
        private const string ConfigFileName = "config.json";
        private readonly string _configFilePath;
        private AppConfig _config;
        private readonly object _lock = new();

        public ConfigurationService()
        {
            _configFilePath = Path.Combine(ConfigDirectory, ConfigFileName);
            _config = new AppConfig();
            
            // 确保配置目录存在
            EnsureConfigDirectoryExists();
            
            // 加载配置
            LoadConfiguration();
        }

        /// <summary>
        /// 获取当前配置
        /// </summary>
        public AppConfig GetConfiguration()
        {
            return _config;
        }

        /// <summary>
        /// 更新配置并保存
        /// </summary>
        public void UpdateConfiguration(Action<AppConfig> updateAction)
        {
            lock (_lock)
            {
                updateAction(_config);
                SaveConfiguration();
            }
        }

        /// <summary>
        /// 更新最后打开的文件夹路径
        /// </summary>
        public void UpdateLastOpenedFolder(string folderPath)
        {
            if (Directory.Exists(folderPath))
            {
                UpdateConfiguration(config => config.LastOpenedFolder = folderPath);
            }
        }

        /// <summary>
        /// 更新最后导出的文件夹路径
        /// </summary>
        public void UpdateLastExportFolder(string folderPath)
        {
            if (Directory.Exists(folderPath))
            {
                UpdateConfiguration(config => config.LastExportFolder = folderPath);
            }
        }

        /// <summary>
        /// 更新FFmpeg路径
        /// </summary>
        public void UpdateFFmpegPath(string ffmpegPath)
        {
            if (File.Exists(ffmpegPath))
            {
                UpdateConfiguration(config => config.FFmpegPath = ffmpegPath);
            }
        }

        /// <summary>
        /// 获取最后打开的文件夹路径
        /// </summary>
        public string GetLastOpenedFolder()
        {
            return _config.LastOpenedFolder;
        }

        /// <summary>
        /// 获取最后导出的文件夹路径
        /// </summary>
        public string GetLastExportFolder()
        {
            return _config.LastExportFolder;
        }

        /// <summary>
        /// 获取FFmpeg路径
        /// </summary>
        public string GetFFmpegPath()
        {
            return _config.FFmpegPath;
        }

        private void EnsureConfigDirectoryExists()
        {
            if (!Directory.Exists(ConfigDirectory))
            {
                Directory.CreateDirectory(ConfigDirectory);
            }
        }

        private void LoadConfiguration()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var json = File.ReadAllText(_configFilePath);
                    var loadedConfig = JsonSerializer.Deserialize<AppConfig>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        WriteIndented = true
                    });

                    if (loadedConfig != null)
                    {
                        _config = loadedConfig;
                        
                        // 验证路径是否仍然存在
                        ValidateAndUpdatePaths();
                    }
                }
                else
                {
                    // 配置文件不存在，创建默认配置
                    SaveConfiguration();
                }
            }
            catch (Exception ex)
            {
                // 如果加载失败，使用默认配置
                System.Diagnostics.Debug.WriteLine($"加载配置失败: {ex.Message}");
                _config = new AppConfig();
            }
        }

        private void ValidateAndUpdatePaths()
        {
            // 验证最后打开的文件夹路径
            if (!Directory.Exists(_config.LastOpenedFolder))
            {
                _config.LastOpenedFolder = Environment.GetFolderPath(Environment.SpecialFolder.MyVideos);
            }

            // 验证最后导出的文件夹路径
            if (!Directory.Exists(_config.LastExportFolder))
            {
                _config.LastExportFolder = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            }

            // 验证FFmpeg路径
            if (!File.Exists(_config.FFmpegPath))
            {
                var defaultFFmpegPath = Path.Combine(AppContext.BaseDirectory, "FFmpeg", "ffmpeg.exe");
                if (File.Exists(defaultFFmpegPath))
                {
                    _config.FFmpegPath = defaultFFmpegPath;
                }
            }
        }

        private void SaveConfiguration()
        {
            try
            {
                var json = JsonSerializer.Serialize(_config, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                File.WriteAllText(_configFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存配置失败: {ex.Message}");
            }
        }
    }
}