# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# 编辑代码注意事项
编辑代码时，注意新增引用类的空间引用是否存在
注意代码修改可能引起的其他代码的影响，必要时一并修改
使用最新版本的C#语法和库的API用法
在回复用户结束任务之前，要使用后台Worker用工具检查并妥善修复可能的编译错误，但不需要你验证运行结果，我会自己验证。

## Project Overview

**Watcher** is a Windows desktop application built with WinUI 3 for video surveillance and vehicle tracking. It provides a three-panel interface for displaying real-time statistics, video playback, and file management.

## Architecture

### Technology Stack
- **Framework**: .NET 8.0 with Windows App SDK (WinUI 3)
- **UI Framework**: WinUI 3 with Mica backdrop
- **Architecture**: MVVM pattern using CommunityToolkit.Mvvm
- **Database**: SQLite with SqlSugar ORM
- **Video Player**: FlyleafLib for video playback
- **Dependency Injection**: Microsoft.Extensions.DependencyInjection

### Key Components

#### 1. Main Window Layout (`MainWindow.xaml:16-28`)
Three-panel layout:
- **Left Panel**: Overview (statistics and charge records)
- **Center Panel**: Video player with controls
- **Right Panel**: File list for video selection

#### 2. Data Models
- **ChargeItem**: Represents individual charge records with time, name, and price
- **VehicleEntry**: Database entity for vehicle counting by hour

#### 3. ViewModels
- **OverviewViewModel**: Manages charge records and user input
- **PlayerViewModel**: Handles video playback logic
- **FileListViewModel**: Manages video file listing and selection
- **VehicleCountService**: Database service for vehicle counting

#### 4. Services
- **VehicleCountService**: SQLite-based vehicle tracking service
- **Dependency Injection**: Configured in `App.xaml.cs:41-50`

## Common Commands

### Build and Development
```bash
# Build the project
dotnet build

# Run the application
dotnet run

# Publish for Windows x64
dotnet publish -c Release -r win-x64 --self-contained

# Publish for Windows x86
dotnet publish -c Release -r win-x86 --self-contained

# Publish for ARM64
dotnet publish -c Release -r win-arm64 --self-contained
```

### Project Structure
```
Watcher/
├── MainWindow.xaml          # Main three-panel UI
├── Overview.xaml           # Charge records display
├── VideoPlayer.xaml        # Video playback controls
├── FileListView.xaml       # Video file browser
├── ViewModels/             # MVVM ViewModels
├── Converters/             # XAML value converters
├── FFmpeg/                 # FFmpeg native libraries
└── Assets/                 # Application icons
```

## Key Features

### Charge Management System
- **Price Mapping**: 5/10/20/30元 maps to 三轮车/轿车/箱货/大箱货
- **Real-time Input**: Numeric input with automatic name assignment
- **Data Persistence**: SQLite database storage
- **Export Functionality**: Export charge records

### Video Integration
- **Flyleaf Player**: Advanced video playback with FFmpeg
- **Progress Tracking**: Seekable timeline with real-time updates
- **File Management**: Browse and select video files

### Vehicle Counting
- **Hourly Tracking**: Count vehicles by type per hour
- **Database Storage**: Persistent vehicle entry records
- **Real-time Updates**: Dynamic statistics based on video time

## Development Notes
a
### Database Setup
- Vehicle counts stored in `VehicleEntry` table
- Charge records managed in-memory with optional export

### Input System
- **Numeric Input**: 0-9 keys for price entry
- **Enter Key**: Confirm charge record
- **Backspace**: Clear current input
- **Delete Key**: Remove selected records

### Configuration
- **Target Framework**: .NET 8.0-windows10.0.19041.0
- **Platforms**: x86, x64, ARM64
- **Language**: Chinese (zh-CN) by default
- **Runtime**: Self-contained deployment supported