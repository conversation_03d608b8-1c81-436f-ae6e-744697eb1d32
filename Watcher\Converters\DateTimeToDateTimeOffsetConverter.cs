using Microsoft.UI.Xaml.Data;
using System;

namespace Watcher
{
    public class DateTimeToDateTimeOffsetConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value == null)
                return DateTimeOffset.Now;
            
            if (value is DateTime dateTime)
                return new DateTimeOffset(dateTime);
            
            return DateTimeOffset.Now;
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            if (value is DateTimeOffset dateTimeOffset)
                return dateTimeOffset.DateTime;
            
            return default(DateTime?);
        }
    }
}