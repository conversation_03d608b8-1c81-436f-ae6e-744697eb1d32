using CommunityToolkit.Mvvm.DependencyInjection;
using FlyleafLib.MediaPlayer;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Controls.Primitives;
using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;
using Microsoft.UI.Xaml.Navigation;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using Windows.Foundation;
using Windows.Foundation.Collections;
using System.ComponentModel;
using System.Diagnostics;

// To learn more about WinUI, the WinUI project structure,
// and more about our project templates, see: http://aka.ms/winui-project-info.

namespace Watcher
{
    /// <summary>
    /// An empty page that can be used on its own or navigated to within a Frame.
    /// </summary>
    public sealed partial class VideoPlayer : Page
    {
        public PlayerViewModel ViewModel { get; set; }
        
        // 防止来自 FlyleafLib 属性变化时触发滑块的 ValueChanged 事件
        private bool curTimeChangedFromLib = false;
        
        // 跟踪是否正在拖拽滑块
        private bool isDragging = false;
        
        // 添加临时禁用交互的标志
        private bool isTemporarilyDisabled = false;
        
        // 用于延迟恢复交互的计时器
        private DispatcherTimer? reEnableTimer;
        
        public VideoPlayer()
        {
            ViewModel = Ioc.Default.GetRequiredService<PlayerViewModel>();
            this.DataContext = ViewModel;
            this.InitializeComponent();
            
            // 订阅ViewModel的属性变化事件，用于更新滑块
            ViewModel.PropertyChanged += ViewModel_PropertyChanged;
            
            // 订阅播放结束事件，强制释放进度条的鼠标捕获
            ViewModel.PlaybackEnded += (sender, e) =>
            {
                Debug.WriteLine("播放结束，彻底重置进度条状态");
                ResetProgressSliderCompletely();
            };
            
            // 初始化计时器
            reEnableTimer = new DispatcherTimer();
            reEnableTimer.Interval = TimeSpan.FromMilliseconds(500); // 延迟500ms
            reEnableTimer.Tick += ReEnableTimer_Tick;
        }

        private void ViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(PlayerViewModel.CurTimeLong))
            {
                // 设置标志，防止滑块 ValueChanged 事件触发播放器跳转
                curTimeChangedFromLib = true;
                
                DispatcherQueue.TryEnqueue(() =>
                {
                    // 只有当播放器有有效时长时才更新滑块值
                    if (ViewModel.TimeDurationLong != 0)
                    {
                        progressSlider.Value = ViewModel.CurTimeLong;
                    }
                    curTimeChangedFromLib = false;
                });
            }
            // 监听播放状态变化，当开始播放时延迟重新启用交互
            else if (e.PropertyName == nameof(PlayerViewModel.IsPlaying))
            {
                if (ViewModel.IsPlaying && isTemporarilyDisabled)
                {
                    Debug.WriteLine("检测到新视频开始播放，准备延迟恢复进度条交互");
                    reEnableTimer?.Start();
                }
            }
        }

        private void ReEnableTimer_Tick(object? sender, object e)
        {
            reEnableTimer?.Stop();
            
            if (isTemporarilyDisabled)
            {
                Debug.WriteLine("延迟时间到，重新启用进度条交互");
                isTemporarilyDisabled = false;
            }
        }

        private void ProgressSlider_ValueChanged(object sender, RangeBaseValueChangedEventArgs e)
        {
            // 如果是来自库的更新，则忽略
            if (curTimeChangedFromLib || isTemporarilyDisabled)
                return;

            // 用户手动改变滑块值时，跳转到对应时间
            var slider = sender as Slider;
            if (slider != null && ViewModel.Player != null)
            {
                // 确保不会超过进度条最大值（总时长-2秒）
                double safeValue = Math.Min(slider.Value, ViewModel.ProgressMaximumLong);
                
                Debug.WriteLine($"用户滑块值改变，原始值: {slider.Value}, 安全值: {safeValue}, 最大允许值: {ViewModel.ProgressMaximumLong}");
                
                ViewModel.Player.CurTime = (long)safeValue;
                
                // 如果值被限制了，更新滑块显示
                if (Math.Abs(safeValue - slider.Value) > 0.1)
                {
                    curTimeChangedFromLib = true;
                    slider.Value = safeValue;
                    curTimeChangedFromLib = false;
                }
            }
        }

        private void ProgressSlider_PointerPressed(object sender, PointerRoutedEventArgs e)
        {
            // 如果临时禁用，直接忽略
            if (isTemporarilyDisabled)
            {
                Debug.WriteLine("进度条交互已临时禁用，忽略PointerPressed事件");
                return;
            }
            
            Debug.WriteLine("进度条按下");
            var slider = sender as Slider;
            if (slider != null)
            {
                // 禁用Tooltip
                var toolTip = ToolTipService.GetToolTip(slider) as ToolTip;
                if (toolTip != null)
                {
                    toolTip.IsEnabled = false;
                }

                isDragging = true;
                slider.CapturePointer(e.Pointer);
                
                // 计算点击位置对应的时间值
                var position = e.GetCurrentPoint(slider);
                double relativePosition = position.Position.X / slider.ActualWidth;
                relativePosition = Math.Max(0, Math.Min(1, relativePosition)); // 限制在 0-1 范围内
                
                double newValue = relativePosition * slider.Maximum;
                
                // 确保不会超过进度条最大值（已经是总时长-2秒）
                newValue = Math.Min(newValue, ViewModel.ProgressMaximumLong);
                
                Debug.WriteLine($"点击进度条，计算位置: {relativePosition}, 新值: {newValue}, 最大允许值: {ViewModel.ProgressMaximumLong}");
                
                // 直接设置播放器时间，不触发滑块 ValueChanged
                if (ViewModel.Player != null)
                {
                    curTimeChangedFromLib = true;
                    slider.Value = newValue;
                    ViewModel.Player.CurTime = (long)newValue;
                    curTimeChangedFromLib = false;
                }
            }
        }

        private void ProgressSlider_PointerCaptureLost(object sender, PointerRoutedEventArgs e)
        {
            Debug.WriteLine("进度条释放");
            isDragging = false;

            var slider = sender as Slider;
            if (slider != null)
            {
                // 恢复Tooltip
                var toolTip = ToolTipService.GetToolTip(slider) as ToolTip;
                if (toolTip != null)
                {
                    toolTip.IsEnabled = true;
                }
            }
        }

        private void ProgressSlider_PointerMoved(object sender, PointerRoutedEventArgs e)
        {
            // 如果临时禁用，直接忽略
            if (isTemporarilyDisabled)
            {
                return;
            }
            
            var slider = sender as Slider;
            if (slider != null)
            {
                // 计算移动位置对应的时间值
                var position = e.GetCurrentPoint(slider);
                double relativePosition = position.Position.X / slider.ActualWidth;
                relativePosition = Math.Max(0, Math.Min(1, relativePosition)); // 限制在 0-1 范围内
                
                double newValue = relativePosition * slider.Maximum;
                
                // 确保不会超过进度条最大值（已经是总时长-2秒）
                newValue = Math.Min(newValue, ViewModel.ProgressMaximumLong);

                // 更新Tooltip
                ViewModel.UpdateProgressToolTip((long)newValue);

                if (isDragging)
                {
                    Debug.WriteLine($"拖拽进度条，位置: {relativePosition}, 新值: {newValue}, 最大允许值: {ViewModel.ProgressMaximumLong}");
                
                    // 实时更新播放器时间和滑块值
                    if (ViewModel.Player != null)
                    {
                        curTimeChangedFromLib = true;
                        slider.Value = newValue;
                        ViewModel.Player.CurTime = (long)newValue;
                        curTimeChangedFromLib = false;
                    }
                }
            }
        }

        private void Play_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.Player?.Play();
        }

        private void Pause_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.Player?.Pause();
        }

        private void Stop_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.Player?.Stop();
        }
        
        /// <summary>
        /// 彻底重置进度条状态，防止在切换视频时仍然处于拖动状态
        /// </summary>
        public void ResetProgressSliderCompletely()
        {
            Debug.WriteLine("开始彻底重置进度条状态");
            
            // 1. 强制停止任何正在进行的计时器
            reEnableTimer?.Stop();
            
            // 2. 释放所有指针捕获
            progressSlider.ReleasePointerCaptures();
            
            // 3. 重置所有状态标志
            isDragging = false;
            curTimeChangedFromLib = false;
            
            // 4. 临时禁用交互，防止新视频加载时的意外触发
            isTemporarilyDisabled = true;
            Debug.WriteLine("进度条交互已临时禁用");
            
            // 5. 重置滑块值到0
            DispatcherQueue.TryEnqueue(() =>
            {
                curTimeChangedFromLib = true;
                progressSlider.Value = 0;
                curTimeChangedFromLib = false;
                Debug.WriteLine("进度条值已重置为0");
            });
        }
        
        /// <summary>
        /// 强制释放进度条的鼠标捕获，防止在切换视频时仍然处于拖动状态
        /// 保留这个方法以保持向后兼容性
        /// </summary>
        public void ForceReleaseProgressSlider()
        {
            ResetProgressSliderCompletely();
        }
    }
}
