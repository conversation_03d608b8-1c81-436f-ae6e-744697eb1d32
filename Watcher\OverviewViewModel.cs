using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.DependencyInjection;
using Microsoft.Identity.Client;
using Microsoft.UI.Xaml.Media;
using Windows.UI;
using Microsoft.UI.Dispatching;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.IO;
using Watcher.Services;
using Watcher.Models;
using OfficeOpenXml;

namespace Watcher
{
    public partial class OverviewViewModel : ObservableObject
    {
        public VehicleCountService VehicleCountService { get; set; }
        private readonly DatabaseService _databaseService;
        public readonly ConfigurationService _configService;

        /// <summary>
        /// 收费项目集合
        /// </summary>
        public ObservableCollection<ChargeItem> ChargeItems { get; set; }

        /// <summary>
        /// 根据日期过滤后的收费记录
        /// </summary>
        public IEnumerable<ChargeItem> FilteredChargeItems
        {
            get
            {
                if (SelectedDate.HasValue)
                {
                    var targetDate = SelectedDate.Value.Date;
                    return ChargeItems.Where(item => item.Time.Date == targetDate);
                }
                return ChargeItems;
            }
        }

        /// <summary>
        /// 最新添加的收费记录
        /// </summary>
        [ObservableProperty]
        private ChargeItem? latestChargeItem;

        /// <summary>
        /// 当前输入的价格字符串
        /// </summary>
        [ObservableProperty]
        private string currentInputPrice = "";

        /// <summary>
        /// 当前显示的名称
        /// </summary>
        [ObservableProperty]
        private string currentInputName = "";

        [ObservableProperty]
        private DateTime? selectedDate;



        /// <summary>
        /// DispatcherQueue 用于UI线程调度
        /// </summary>
        public DispatcherQueue DispatcherQueue { get; set; }

        // 缓存颜色画刷对象，避免频繁创建
        private static readonly SolidColorBrush _normalBackgroundBrush = new SolidColorBrush(Windows.UI.Color.FromArgb(255, 45, 45, 48)); // 深灰色背景
        private static readonly SolidColorBrush _matchBackgroundBrush = new SolidColorBrush(Windows.UI.Color.FromArgb(255, 20, 80, 40)); // 暗绿色
        private static readonly SolidColorBrush _normalForegroundBrush = new SolidColorBrush(Windows.UI.Color.FromArgb(255, 255, 255, 255));

        [ObservableProperty]
        private SolidColorBrush _inputBackgroundColor;

        [ObservableProperty]
        private SolidColorBrush _inputForegroundColor;

        /// <summary>
        /// 当前输入显示文本（价格 + 名称）
        /// </summary>
        public string CurrentInputDisplay 
        { 
            get
            {
                if (string.IsNullOrEmpty(CurrentInputPrice))
                    return "";
                
                return $"{CurrentInputPrice}元 - {CurrentInputName}";
            }
        }

        public OverviewViewModel()
        {
            VehicleCountService = Ioc.Default.GetRequiredService<VehicleCountService>();
            _configService = Ioc.Default.GetRequiredService<ConfigurationService>();
            _databaseService = new DatabaseService();

            // 初始化收费项目集合
            ChargeItems = new ObservableCollection<ChargeItem>();

            // 初始化输入颜色
            InputBackgroundColor = _normalBackgroundBrush;
            InputForegroundColor = _normalForegroundBrush;

            // 默认选择昨天
            SelectedDate = DateTime.Now.AddDays(-1).Date;

            // 加载数据库中的记录
            LoadChargeRecordsAsync();
        }

        /// <summary>
        /// 设置 DispatcherQueue
        /// </summary>
        /// <param name="dispatcherQueue"></param>
        public void SetDispatcherQueue(DispatcherQueue dispatcherQueue)
        {
            DispatcherQueue = dispatcherQueue;
        }

        /// <summary>
        /// 添加数字到输入价格
        /// </summary>
        /// <param name="digit">输入的数字字符</param>
        public void AddDigitToInput(char digit)
        {
            CurrentInputPrice += digit;
            UpdateCurrentInputNameAndColor();
            // 批量更新UI相关属性
            UpdateInputDisplayProperties();
        }

        /// <summary>
        /// 根据价格更新名称和背景色
        /// </summary>
        private void UpdateCurrentInputNameAndColor()
        {
            if (decimal.TryParse(CurrentInputPrice, out decimal price))
            {
                CurrentInputName = GetNameByPrice(price);
                if (IsValidPrice(price))
                {
                    InputBackgroundColor = _matchBackgroundBrush;
                }
                else
                {
                    InputBackgroundColor = _normalBackgroundBrush;
                }
            }
            else
            {
                CurrentInputName = "";
                InputBackgroundColor = _normalBackgroundBrush;
            }
        }

        /// <summary>
        /// 批量更新输入显示相关的属性，减少UI刷新次数
        /// </summary>
        private void UpdateInputDisplayProperties()
        {
            OnPropertyChanged(nameof(CurrentInputDisplay));
        }

        /// <summary>
        /// 检查价格是否有效
        /// </summary>
        /// <param name="price">价格</param>
        /// <returns>是否有效</returns>
        partial void OnSelectedDateChanged(DateTime? value)
    {
        OnPropertyChanged(nameof(FilteredChargeItems));
        OnPropertyChanged(nameof(ChargeTitle));
    }

    /// <summary>
    /// 收费记录标题，包括当前日期
    /// </summary>
    public string ChargeTitle => $"收费记录 - {SelectedDate?.ToString("yyyy-MM-dd") ?? "所有日期"}";

    private bool IsValidPrice(decimal price)
    {
        return price == 5 || price == 10 || price == 20 || price == 30;
    }

        /// <summary>
        /// 根据价格获取对应的名称
        /// </summary>
        /// <param name="price">价格</param>
        /// <returns>对应的名称</returns>
        private string GetNameByPrice(decimal price)
        {
            return price switch
            {
                5 => "三轮车",
                10 => "轿车",
                20 => "箱货",
                30 => "大箱货",
                _ => "无效"
            };
        }

        /// <summary>
        /// 确认输入，添加新的收费记录
        /// </summary>
        /// <param name="realTime">真实时间</param>
        public async Task ConfirmInput(DateTime realTime)
        {
            // 检查输入是否有效
            if (!string.IsNullOrEmpty(CurrentInputPrice) && decimal.TryParse(CurrentInputPrice, out decimal price))
            {
                if (IsValidPrice(price))
                {
                    // 创建收费记录
                    var chargeItem = new ChargeItem(realTime, CurrentInputName, price);
                    ChargeItems.Add(chargeItem); // 使用Add而不是Insert，性能更好
                    
                    // 设置最新记录
                    LatestChargeItem = chargeItem;
                    
                    // 通知过滤后的集合已更新
                    OnPropertyChanged(nameof(FilteredChargeItems));
                    
                    // 保存到数据库
                    await SaveChargeRecordAsync(chargeItem);
                }
                // 输入无效时不做任何提示，直接清空输入
            }
            // 无论输入是否有效，都清空输入以便继续输入
            ClearInput();
        }

        /// <summary>
        /// 清空当前输入
        /// </summary>
        public void ClearInput()
        {
            // 批量更新所有属性，减少UI刷新次数
            CurrentInputPrice = "";
            CurrentInputName = "";
            InputBackgroundColor = _normalBackgroundBrush;
            
            // 一次性更新所有相关的UI属性
            UpdateInputDisplayProperties();
        }

        /// <summary>
        /// 加载数据库中的收费记录
        /// </summary>
        private async void LoadChargeRecordsAsync()
        {
            try
            {
                var records = await _databaseService.GetAllChargeRecordsAsync();
                foreach (var record in records)
                {
                    ChargeItems.Add(new ChargeItem(record));
                }
            }
            catch (Exception ex)
            {
                // 如果数据库加载失败，静默处理
                System.Diagnostics.Debug.WriteLine($"加载收费记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存收费记录到数据库
        /// </summary>
        private async Task SaveChargeRecordAsync(ChargeItem chargeItem)
        {
            try
            {
                var record = new ChargeRecord
                {
                    ChargeTime = chargeItem.Time,
                    VehicleName = chargeItem.Name,
                    Amount = chargeItem.Price
                };
                
                var id = await _databaseService.AddChargeRecordAsync(record);
                chargeItem.Id = id; // 设���数据库生成的ID
            }
            catch (Exception ex)
            {
                // 如果保存失败，静默处理（数据仍在内存中）
                System.Diagnostics.Debug.WriteLine($"保存收费记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从数据库删除收费记录
        /// </summary>
        private async Task DeleteChargeRecordAsync(int recordId)
        {
            try
            {
                await _databaseService.DeleteChargeRecordAsync(recordId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除收费记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除指定的收费记录
        /// </summary>
        /// <param name="chargeItem">要删除的收费记录</param>
        public async void DeleteChargeItem(ChargeItem chargeItem)
        {
            if (chargeItem != null && ChargeItems.Contains(chargeItem))
            {
                ChargeItems.Remove(chargeItem);
                
                // 从数据库删除
                if (chargeItem.Id > 0)
                {
                    await DeleteChargeRecordAsync(chargeItem.Id);
                }
            }
        }

        /// <summary>
        /// 删除选中的收费记录
        /// </summary>
        /// <param name="selectedItems">选中的项目集合</param>
        public async void DeleteSelectedItems(System.Collections.Generic.IList<object> selectedItems)
        {
            if (selectedItems == null || selectedItems.Count == 0)
                return;

            // 创建要删除的项目列表，避免在遍历时修改集合
            var itemsToDelete = new List<ChargeItem>();
            foreach (var item in selectedItems)
            {
                if (item is ChargeItem chargeItem)
                {
                    itemsToDelete.Add(chargeItem);
                }
            }

            // 删除所有选中的项目
            foreach (var item in itemsToDelete)
            {
                ChargeItems.Remove(item);

                // 从数据库删除
                if (item.Id > 0)
                {
                    await DeleteChargeRecordAsync(item.Id);
                }
            }

            // 通知过滤后的集合已更新
            OnPropertyChanged(nameof(FilteredChargeItems));
        }

        /// <summary>
        /// 删除选中的收费记录（异步版本）
        /// </summary>
        /// <param name="selectedItems">选中的项目集合</param>
        /// <returns>删除操作的任务</returns>
        public async Task DeleteSelectedItemsAsync(System.Collections.Generic.IList<object> selectedItems)
        {
            if (selectedItems == null || selectedItems.Count == 0)
                return;

            // 创建要删除的项目列表，避免在遍历时修改集合
            var itemsToDelete = new List<ChargeItem>();
            foreach (var item in selectedItems)
            {
                if (item is ChargeItem chargeItem)
                {
                    itemsToDelete.Add(chargeItem);
                }
            }

            // 删除所有选中的项目
            foreach (var item in itemsToDelete)
            {
                ChargeItems.Remove(item);

                // 从数据库删除
                if (item.Id > 0)
                {
                    await DeleteChargeRecordAsync(item.Id);
                }
            }

            // 通知过滤后的集合已更新
            OnPropertyChanged(nameof(FilteredChargeItems));
        }

        /// <summary>
        /// 导出收费记录到Excel文件
        /// </summary>
        /// <param name="filePath">导出文件路径</param>
        /// <param name="startDate">开始日期（可选）</param>
        /// <param name="endDate">结束日期（可选）</param>
        /// <returns>是否导出成功</returns>
        public async Task<bool> ExportToExcelAsync(string filePath, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                // 设置EPPlus许可证（非商业使用）
                ExcelPackage.License.SetNonCommercialPersonal("<frey>"); //This will also set the Author property to the name provided in the argument.

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("收费记录");

                // 设置标题行
                worksheet.Cells[1, 1].Value = "收费记录";
                worksheet.Cells[1, 1].Style.Font.Size = 16;
                worksheet.Cells[1, 1].Style.Font.Bold = true;
                worksheet.Cells[1, 1, 1, 4].Merge = true;
                worksheet.Cells[1, 1, 1, 4].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;

                // 设置列标题
                worksheet.Cells[3, 1].Value = "序号";
                worksheet.Cells[3, 2].Value = "时间";
                worksheet.Cells[3, 3].Value = "项目名称";
                worksheet.Cells[3, 4].Value = "金额(元)";

                // 设置标题样式
                using (var range = worksheet.Cells[3, 1, 3, 4])
                {
                    range.Style.Font.Bold = true;
                    range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
                    range.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                }

                // 筛选记录
                var records = ChargeItems.AsEnumerable();
                if (startDate.HasValue)
                {
                    records = records.Where(r => r.Time >= startDate.Value);
                }
                if (endDate.HasValue)
                {
                    records = records.Where(r => r.Time <= endDate.Value.AddDays(1).AddTicks(-1));
                }

                var filteredRecords = records.ToList();

                // 填充数据
                int row = 4;
                decimal totalAmount = 0;
                foreach (var item in filteredRecords)
                {
                    worksheet.Cells[row, 1].Value = row - 3;
                    worksheet.Cells[row, 2].Value = item.Time.ToString("yyyy-MM-dd HH:mm:ss");
                    worksheet.Cells[row, 3].Value = item.Name;
                    worksheet.Cells[row, 4].Value = item.Price;
                    totalAmount += item.Price;
                    row++;
                }

                // 添加统计信息
                if (filteredRecords.Any())
                {
                    // 空一行
                    row++;
                    
                    // 总计
                    worksheet.Cells[row, 3].Value = "总计:";
                    worksheet.Cells[row, 3].Style.Font.Bold = true;
                    worksheet.Cells[row, 3].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;
                    worksheet.Cells[row, 4].Value = totalAmount;
                    worksheet.Cells[row, 4].Style.Font.Bold = true;
                    worksheet.Cells[row, 4].Style.Numberformat.Format = "#,##0.00";
                }

                // 自动调整列宽
                worksheet.Column(1).AutoFit(5, 10);
                worksheet.Column(2).AutoFit(10, 20);
                worksheet.Column(3).AutoFit(10, 15);
                worksheet.Column(4).AutoFit(5, 12);

                // 设置边框
                if (filteredRecords.Any())
                {
                    using (var range = worksheet.Cells[3, 1, row - 3, 4])
                    {
                        range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                        range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                        range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                        range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                    }
                }

                // 保存文件
                var fileInfo = new FileInfo(filePath);
                await package.SaveAsAsync(fileInfo);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导出Excel失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 按月导出收费记录
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="month">月份</param>
        /// <param name="filePath">导出文件路径</param>
        /// <returns></returns>
        public async Task<bool> ExportMonthlyRecordsAsync(int year, int month, string filePath)
        {
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1);
            return await ExportToExcelAsync(filePath, startDate, endDate);
        }

        /// <summary>
        /// 按日导出收费记录
        /// </summary>
        /// <param name="date">指定日期</param>
        /// <param name="filePath">导出文件路径</param>
        /// <returns></returns>
        public async Task<bool> ExportDailyRecordsAsync(DateTime date, string filePath)
        {
            return await ExportToExcelAsync(filePath, date, date);
        }
    }
}