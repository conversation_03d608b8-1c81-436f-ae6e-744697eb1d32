using System;
using System.IO;
using SqlSugar;
using Watcher.Models;

namespace Watcher.Services
{
    /// <summary>
    /// 数据库初始化服务，负责首次启动时的数据库创建和初始化
    /// </summary>
    public class DatabaseInitializationService
    {
        private const string DbFileName = "watcher.db";

        /// <summary>
        /// 初始化所有数据库
        /// </summary>
        public static void InitializeAllDatabases()
        {
            InitializeDatabase();
        }

        /// <summary>
        /// 初始化数据库
        /// </summary>
        private static void InitializeDatabase()
        {
            try
            {
                var dbPath = GetDbPath();
                
                // 检查数据库文件是否存在
                if (!File.Exists(dbPath))
                {
                    Console.WriteLine("检测到首次启动，正在创建数据库...");
                }

                // 创建数据库连接并初始化表结构
                var db = new SqlSugarClient(new ConnectionConfig()
                {
                    ConnectionString = $"Data Source={dbPath}",
                    DbType = DbType.Sqlite,
                    IsAutoCloseConnection = true,
                    InitKeyType = InitKeyType.Attribute
                });

                // 创建所有数据表
                db.CodeFirst.InitTables<VehicleEntry>();
                db.CodeFirst.InitTables<ChargeRecord>();
                
                Console.WriteLine("数据库初始化完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"初始化数据库时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 检查数据库是否已初始化
        /// </summary>
        /// <returns>如果数据库文件存在则返回true</returns>
        public static bool IsDatabaseInitialized()
        {
            try
            {
                return File.Exists(GetDbPath());
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取数据库完整路径
        /// </summary>
        public static string GetDbPath()
        {
            //return Path.Combine(Environment.CurrentDirectory, DbFileName);
            return @"D:\watcher.db";
        }
    }
}