using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;
using Microsoft.UI.Xaml.Input;
using Windows.System;
using CommunityToolkit.Mvvm.ComponentModel;
using System.Collections.ObjectModel;
using System.Diagnostics;

namespace Watcher
{
    // 车辆进出记录类
    public class VehicleEntry : ObservableObject
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        private DateTime time;
        public DateTime Time
        {
            get => time;
            set => SetProperty(ref time, value);
        }
        private int hour;
        public int Hour
        {
            get => hour;
            set => SetProperty(ref hour, value);
        }

        private int car1;
        public int Car1
        {
            get => car1;
            set => SetProperty(ref car1, value);
        }

        private int car2;
        public int Car2
        {
            get => car2;
            set => SetProperty(ref car2, value);
        }

        private int car3;
        public int Car3
        {
            get => car3;
            set => SetProperty(ref car3, value);
        }

        private int car4;
        public int Car4
        {
            get => car4;
            set => SetProperty(ref car4, value);
        }

        private int car5;
        public int Car5
        {
            get => car5;
            set => SetProperty(ref car5, value);
        }
    }

    public partial class VehicleCountService : ObservableObject
    {
        private SqlSugarClient _db;
        [ObservableProperty]
        private ObservableCollection<VehicleEntry> entries = new ObservableCollection<VehicleEntry>();
        private VehicleEntry? lastRecord;
        [ObservableProperty]
        private string _currentTimeRangeText = string.Empty;
        public VehicleCountService(string? dbPath = null)
        {
            // 使用数据库初始化服务提供的路径
            string actualDbPath = dbPath ?? Services.DatabaseInitializationService.GetDbPath();
            
            _db = new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = $"DataSource={actualDbPath}",
                DbType = DbType.Sqlite,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute
            });
            // 创建数据库表
            _db.CodeFirst.InitTables<VehicleEntry>();
        }
        public void LoadByTimeRange(DateTime start, DateTime end)
        {

            // 取该日期9点到次日9点之间的数据
            LoadEntries(start);
        }
        public void LoadEntries(DateTime time)
        {
            Entries.Clear();

            var (start, end) = Tools.GetTimeRange(time);
            var records = _db.Queryable<VehicleEntry>()
                            .Where(v => v.Time >= start && v.Time < end)
                            .OrderBy(v => v.Time)
                            .ToList();

            foreach (var record in records)
            {
                Entries.Add(record);
                Debug.WriteLine($"LoadEntries: {record.Time}, {record.Car1}, {record.Car2}, {record.Car3}, {record.Car4}, {record.Car5}");
            }
        }

        public void InsertOrUpdateVehicleEntryCount(DateTime time, string vehicleType)
        {
            Debug.WriteLine($"InsertOrUpdateVehicleEntryCount: {time}, {vehicleType}");
            var hour = time.Hour;

            var (start, end) = Tools.GetTimeRange(time);

            lastRecord = Entries.Where(v => v.Time.Date == time.Date &&
                               v.Time.Hour == time.Hour)
                   .FirstOrDefault();

            if (lastRecord?.Hour == hour)
            {
                UpdateLastVehicleCount(vehicleType, 1);
            }
            else
            {
                var newRecord = new VehicleEntry
                {
                    Time = time,
                    Hour = time.Hour,
                    Car1 = vehicleType == "Car1" ? 1 : 0,
                    Car2 = vehicleType == "Car2" ? 1 : 0,
                    Car3 = vehicleType == "Car3" ? 1 : 0,
                    Car4 = vehicleType == "Car4" ? 1 : 0,
                    Car5 = vehicleType == "Car5" ? 1 : 0
                };
                newRecord.Id = _db.Insertable(newRecord).ExecuteReturnIdentity();
                
                // 更新视图，找到合适的插入位置
                int insertIndex = Entries.TakeWhile(e => e.Time < newRecord.Time).Count();
                Entries.Insert(insertIndex, newRecord);
            }
        }

        private void UpdateLastVehicleCount(string vehicleType,int value)
        {
            if(lastRecord == null)
                return;

            switch (vehicleType)
            {
                case "Car1":
                    lastRecord.Car1 += value;

                    break;
                case "Car2":
                    lastRecord.Car2 += value;
                    break;
                case "Car3":
                    lastRecord.Car3 += value;
                    break;
                case "Car4":
                    lastRecord.Car4 += value;
                    break;
                case "Car5":
                    lastRecord.Car5 += value;
                    break;
            }
            _db.Updateable(lastRecord).ExecuteCommand();
        }

        public void UndoLastInsert()
        {
            var lastRecord = _db.Queryable<VehicleEntry>()
                                .OrderByDescending(v => v.Id)
                                .First();

            if (lastRecord != null)
            {
                if (lastRecord.Car1 > 0) lastRecord.Car1 -= 1;
                else if (lastRecord.Car2 > 0) lastRecord.Car2 -= 1;
                else if (lastRecord.Car3 > 0) lastRecord.Car3 -= 1;
                else if (lastRecord.Car4 > 0) lastRecord.Car4 -= 1;
                else if (lastRecord.Car5 > 0) lastRecord.Car5 -= 1;

                if (lastRecord.Car1 == 0 && lastRecord.Car2 == 0 && lastRecord.Car3 == 0 && lastRecord.Car4 == 0 && lastRecord.Car5 == 0)
                {
                    _db.Deleteable<VehicleEntry>().In(lastRecord.Id).ExecuteCommand();
                    Entries.Remove(lastRecord);
                }
                else
                {
                    _db.Updateable(lastRecord).ExecuteCommand();
                }
            }
        }
    }
}