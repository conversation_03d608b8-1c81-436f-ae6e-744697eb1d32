using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.IO;
using System.Threading.Tasks;
using Watcher;
using Watcher.Services;
using Microsoft.Extensions.DependencyInjection;
using CommunityToolkit.Mvvm.DependencyInjection;

namespace WatcherTest
{
    [TestClass]
    public class FileListViewModelTest
    {
        private string _testFolder;
        private FileListViewModel _viewModel;

        [TestInitialize]
        public void Setup()
        {
            // 创建测试文件夹
            _testFolder = Path.Combine(Path.GetTempPath(), "WatcherTest_" + Guid.NewGuid().ToString("N")[..8]);
            Directory.CreateDirectory(_testFolder);

            // 配置依赖注入
            var services = new ServiceCollection()
                .AddSingleton<ConfigurationService>()
                .BuildServiceProvider();

            Ioc.Default.ConfigureServices(services);

            // 创建测试视频文件
            CreateTestVideoFiles();

            _viewModel = new FileListViewModel();
        }

        [TestCleanup]
        public void Cleanup()
        {
            // 清理测试文件夹
            if (Directory.Exists(_testFolder))
            {
                Directory.Delete(_testFolder, true);
            }
        }

        private void CreateTestVideoFiles()
        {
            // 创建一些测试视频文件
            var testFiles = new[]
            {
                "594329021_1_20241018T231930Z_20241018T234931Z.mp4",
                "594329021_1_20241019T231930Z_20241019T234931Z.mp4",
                "594329021_1_20241020T231930Z_20241020T234931Z.mp4"
            };

            foreach (var fileName in testFiles)
            {
                var filePath = Path.Combine(_testFolder, fileName);
                File.WriteAllText(filePath, "test content");
            }
        }

        [TestMethod]
        public async Task LoadVideos_ShouldNotCauseDuplicateLoading()
        {
            // Arrange
            _viewModel.SelectedFolder = _testFolder;
            var initialCount = 0;
            var loadCount = 0;

            // 监听FilteredVideoItems的变化
            _viewModel.FilteredVideoItems.CollectionChanged += (s, e) =>
            {
                if (e.Action == System.Collections.Specialized.NotifyCollectionChangedAction.Add)
                {
                    loadCount++;
                }
                else if (e.Action == System.Collections.Specialized.NotifyCollectionChangedAction.Reset)
                {
                    initialCount++;
                }
            };

            // Act
            _viewModel.LoadVideos();
            
            // 等待异步操作完成
            await Task.Delay(1000);

            // Assert
            Assert.IsTrue(_viewModel.FilteredVideoItems.Count > 0, "应该加载到视频文件");
            
            // 验证没有重复加载 - 应该只有一次Reset（清空）和一次或多次Add（添加文件）
            // 但不应该有多次完整的重新加载
            Console.WriteLine($"Reset count: {initialCount}, Add count: {loadCount}");
            Console.WriteLine($"Final video count: {_viewModel.FilteredVideoItems.Count}");
            
            // 如果修复成功，应该只有一次清空操作
            Assert.IsTrue(initialCount <= 2, $"重置次数过多: {initialCount}，可能存在重复加载问题");
        }

        [TestMethod]
        public async Task SelectedDateChange_ShouldNotCauseInfiniteLoop()
        {
            // Arrange
            _viewModel.SelectedFolder = _testFolder;
            var refreshCallCount = 0;
            
            // 通过反射监听RefreshAsync的调用（这里简化为监听FilteredVideoItems的Reset）
            _viewModel.FilteredVideoItems.CollectionChanged += (s, e) =>
            {
                if (e.Action == System.Collections.Specialized.NotifyCollectionChangedAction.Reset)
                {
                    refreshCallCount++;
                }
            };

            // Act
            _viewModel.LoadVideos();
            await Task.Delay(500);
            
            var initialRefreshCount = refreshCallCount;
            
            // 手动更改日期
            _viewModel.SelectedDate = DateTime.Today.AddDays(-1);
            await Task.Delay(500);

            // Assert
            var finalRefreshCount = refreshCallCount;
            var additionalRefreshes = finalRefreshCount - initialRefreshCount;
            
            Console.WriteLine($"Initial refresh count: {initialRefreshCount}");
            Console.WriteLine($"Final refresh count: {finalRefreshCount}");
            Console.WriteLine($"Additional refreshes after date change: {additionalRefreshes}");
            
            // 手动更改日期应该只触发一次额外的刷新
            Assert.IsTrue(additionalRefreshes <= 1, $"日期更改触发了过多的刷新: {additionalRefreshes}");
        }
    }
}
